#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境测试脚本
"""

import sys
import os

def main():
    print("🧪 环境测试")
    print("=" * 30)
    
    # Python版本
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 当前目录
    print(f"当前目录: {os.getcwd()}")
    
    # 检查文件
    files_to_check = [
        "fixed_browser_environment.py",
        "fixed_login_service.py", 
        "fixed_cookie_validator.py",
        "run_fixed_system.py"
    ]
    
    print("\n📁 文件检查:")
    for file_name in files_to_check:
        exists = os.path.exists(file_name)
        status = "✅" if exists else "❌"
        print(f"  {status} {file_name}")
    
    # 检查模块导入
    print("\n📦 模块检查:")
    modules_to_check = ['asyncio', 'json', 'os', 'time', 'datetime']
    
    for module_name in modules_to_check:
        try:
            __import__(module_name)
            print(f"  ✅ {module_name}")
        except ImportError:
            print(f"  ❌ {module_name}")
    
    # 检查playwright
    try:
        import playwright
        print(f"  ✅ playwright (已安装)")
    except ImportError:
        print(f"  ❌ playwright (未安装)")
    
    print("\n✅ 环境测试完成")

if __name__ == "__main__":
    main()
