#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复版登录系统
"""

import asyncio
import sys
import os
from datetime import datetime
from fixed_login_service import FixedLoginService
from fixed_cookie_validator import FixedCookieValidator

class LoginSystemTester:
    """登录系统测试器"""
    
    def __init__(self):
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {details}")
    
    async def test_environment_creation(self, phone_number: str) -> bool:
        """测试环境创建"""
        try:
            from fixed_browser_environment import FixedBrowserEnvironment
            
            env = FixedBrowserEnvironment(phone_number)
            
            # 测试环境ID生成
            if not env.environment_id:
                self.log_test("环境ID生成", False, "环境ID为空")
                return False
            
            self.log_test("环境ID生成", True, f"ID: {env.environment_id}")
            
            # 测试浏览器初始化
            init_success = await env.init_browser()
            self.log_test("浏览器初始化", init_success, "浏览器环境创建")
            
            if init_success:
                # 测试指纹收集
                fingerprint = await env.collect_environment_fingerprint()
                has_fingerprint = bool(fingerprint and len(fingerprint) > 5)
                self.log_test("环境指纹收集", has_fingerprint, f"收集到 {len(fingerprint)} 个参数")
                
                await env.close()
                return has_fingerprint
            
            return False
            
        except Exception as e:
            self.log_test("环境创建测试", False, f"异常: {e}")
            return False
    
    async def test_cookie_validation(self) -> bool:
        """测试cookie验证"""
        try:
            validator = FixedCookieValidator()
            
            # 查找现有环境
            valid_env = await validator.find_valid_environment()
            
            if valid_env:
                self.log_test("环境文件查找", True, f"找到: {os.path.basename(valid_env)}")
                
                # 验证环境
                is_valid, details = await validator.validate_environment_cookies(valid_env)
                
                success_rate = details.get('success_rate', 0)
                self.log_test("Cookie验证", is_valid, f"成功率: {success_rate:.1%}")
                
                # 详细测试结果
                for test_name, result in details.get('validation_results', {}).items():
                    test_valid = result.get('valid', False)
                    test_details = result.get('test_name', test_name)
                    self.log_test(f"  └─ {test_details}", test_valid, "")
                
                return is_valid
            else:
                self.log_test("环境文件查找", False, "未找到有效环境文件")
                return False
                
        except Exception as e:
            self.log_test("Cookie验证测试", False, f"异常: {e}")
            return False
    
    async def test_login_service(self, phone_number: str, mock_mode: bool = True) -> bool:
        """测试登录服务"""
        try:
            login_service = FixedLoginService(phone_number)
            
            # 测试冷却检查
            cooldown_ok = await login_service.check_cooldown()
            self.log_test("登录冷却检查", cooldown_ok, "冷却时间检查")
            
            if mock_mode:
                # 模拟模式，不实际登录
                self.log_test("登录服务初始化", True, "模拟模式，跳过实际登录")
                return True
            else:
                # 实际登录测试（需要用户交互）
                print("\n⚠️ 即将进行实际登录测试，需要手机验证码")
                confirm = input("是否继续？(y/N): ").strip().lower()
                
                if confirm != 'y':
                    self.log_test("登录服务测试", False, "用户取消")
                    return False
                
                success = await login_service.run_fixed_login()
                self.log_test("完整登录流程", success, "实际登录测试")
                
                await login_service.close()
                return success
                
        except Exception as e:
            self.log_test("登录服务测试", False, f"异常: {e}")
            return False
    
    def test_file_structure(self) -> bool:
        """测试文件结构"""
        try:
            required_files = [
                "fixed_browser_environment.py",
                "fixed_login_service.py", 
                "fixed_cookie_validator.py"
            ]
            
            missing_files = []
            for file_name in required_files:
                if not os.path.exists(file_name):
                    missing_files.append(file_name)
            
            if missing_files:
                self.log_test("文件结构检查", False, f"缺少文件: {', '.join(missing_files)}")
                return False
            else:
                self.log_test("文件结构检查", True, "所有必需文件存在")
                return True
                
        except Exception as e:
            self.log_test("文件结构检查", False, f"异常: {e}")
            return False
    
    def test_directory_structure(self) -> bool:
        """测试目录结构"""
        try:
            # 创建必要目录
            directories = ["browser_environments", "screenshots"]
            
            for dir_name in directories:
                if not os.path.exists(dir_name):
                    os.makedirs(dir_name, exist_ok=True)
                    self.log_test(f"创建目录 {dir_name}", True, "目录已创建")
                else:
                    self.log_test(f"检查目录 {dir_name}", True, "目录已存在")
            
            return True
            
        except Exception as e:
            self.log_test("目录结构检查", False, f"异常: {e}")
            return False
    
    async def run_comprehensive_test(self, phone_number: str, include_login: bool = False):
        """运行综合测试"""
        print("🧪 开始修复版登录系统综合测试")
        print("=" * 50)
        
        # 基础测试
        self.test_file_structure()
        self.test_directory_structure()
        
        # 环境测试
        await self.test_environment_creation(phone_number)
        
        # Cookie验证测试
        await self.test_cookie_validation()
        
        # 登录服务测试
        await self.test_login_service(phone_number, mock_mode=not include_login)
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        
        print("\n" + "=" * 50)
        print("📊 测试结果统计")
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        print(f"成功率: {passed_tests/total_tests:.1%}")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！修复版系统可以使用")
        elif passed_tests >= total_tests * 0.8:
            print("⚠️ 大部分测试通过，系统基本可用")
        else:
            print("❌ 多个测试失败，需要进一步修复")
        
        return passed_tests >= total_tests * 0.8

async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python test_fixed_login.py <手机号> [--full]")
        print("  --full: 包含实际登录测试（需要验证码）")
        sys.exit(1)
    
    phone_number = sys.argv[1]
    include_login = "--full" in sys.argv
    
    if not phone_number.isdigit() or len(phone_number) != 11:
        print("❌ 请输入有效的11位手机号")
        sys.exit(1)
    
    tester = LoginSystemTester()
    
    try:
        success = await tester.run_comprehensive_test(phone_number, include_login)
        
        if success:
            print("\n✅ 测试完成，系统可以使用")
            print("\n🚀 下一步操作:")
            print(f"  python fixed_login_service.py {phone_number}")
        else:
            print("\n❌ 测试未完全通过，请检查错误信息")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
