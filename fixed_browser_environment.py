#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版浏览器环境管理器
解决cookie失效和登录循环问题
"""

import json
import os
import time
import hashlib
import asyncio
from datetime import datetime
from playwright.async_api import async_playwright
from typing import Dict, Any, Optional, List

class FixedBrowserEnvironment:
    """修复版浏览器环境管理器"""
    
    def __init__(self, phone_number: str):
        self.phone_number = phone_number
        self.environment_id = self._generate_environment_id()
        self.environment_file = f"browser_environments/fixed_{self.environment_id}.json"
        self.cookie_file = f"cookies_fixed_{self.environment_id}.json"
        self.browser = None
        self.context = None
        self.page = None
        self.login_retry_count = 0
        self.max_login_retries = 3
        self.login_cooldown = 60  # 60秒冷却时间
        self.last_login_attempt = 0
        
    def _generate_environment_id(self) -> str:
        """生成环境ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        phone_hash = hashlib.md5(self.phone_number.encode()).hexdigest()[:8]
        return f"{timestamp}_{phone_hash}"
    
    async def init_browser(self) -> bool:
        """初始化浏览器"""
        try:
            print("🚀 初始化修复版浏览器环境...")
            
            playwright = await async_playwright().start()
            
            # 使用固定的浏览器配置
            self.browser = await playwright.chromium.launch(
                headless=False,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-dev-shm-usage',
                    '--no-first-run',
                    '--disable-default-apps',
                    '--disable-extensions',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding'
                ]
            )
            
            # 创建上下文
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                locale='zh-CN',
                timezone_id='Asia/Shanghai'
            )
            
            # 创建页面
            self.page = await self.context.new_page()
            
            # 注入反检测脚本
            await self._inject_anti_detection()
            
            print("✅ 浏览器环境初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ 浏览器初始化失败: {e}")
            return False
    
    async def _inject_anti_detection(self):
        """注入反检测脚本"""
        anti_detection_script = """
        // 隐藏webdriver属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // 修改plugins
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        // 修改languages
        Object.defineProperty(navigator, 'languages', {
            get: () => ['zh-CN', 'zh', 'en'],
        });
        
        // 修改permissions
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
        
        // 修改chrome对象
        window.chrome = {
            runtime: {},
        };
        """
        
        await self.page.add_init_script(anti_detection_script)
    
    async def collect_environment_fingerprint(self) -> Dict[str, Any]:
        """收集完整的浏览器环境指纹"""
        print("🔍 收集浏览器环境指纹...")
        
        try:
            fingerprint = await self.page.evaluate("""
                () => {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    ctx.textBaseline = 'top';
                    ctx.font = '14px Arial';
                    ctx.fillText('Browser fingerprint', 2, 2);
                    
                    const gl = document.createElement('canvas').getContext('webgl');
                    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                    
                    return {
                        userAgent: navigator.userAgent,
                        language: navigator.language,
                        languages: navigator.languages,
                        platform: navigator.platform,
                        cookieEnabled: navigator.cookieEnabled,
                        doNotTrack: navigator.doNotTrack,
                        hardwareConcurrency: navigator.hardwareConcurrency,
                        maxTouchPoints: navigator.maxTouchPoints,
                        screenWidth: screen.width,
                        screenHeight: screen.height,
                        screenColorDepth: screen.colorDepth,
                        screenPixelDepth: screen.pixelDepth,
                        timezoneOffset: new Date().getTimezoneOffset(),
                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                        canvasFingerprint: canvas.toDataURL(),
                        webglVendor: debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : null,
                        webglRenderer: debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : null,
                        plugins: Array.from(navigator.plugins).map(p => p.name),
                        mimeTypes: Array.from(navigator.mimeTypes).map(m => m.type),
                        localStorage: typeof(Storage) !== "undefined",
                        sessionStorage: typeof(Storage) !== "undefined",
                        indexedDB: typeof(indexedDB) !== "undefined",
                        webWorker: typeof(Worker) !== "undefined",
                        webRTC: typeof(RTCPeerConnection) !== "undefined"
                    };
                }
            """)
            
            # 添加时间戳
            fingerprint['timestamp'] = datetime.now().isoformat()
            fingerprint['environment_id'] = self.environment_id
            fingerprint['phone_number'] = self.phone_number
            
            print("✅ 环境指纹收集完成")
            return fingerprint
            
        except Exception as e:
            print(f"❌ 环境指纹收集失败: {e}")
            return {}
    
    async def save_environment(self, cookies: List[Dict], fingerprint: Dict[str, Any]):
        """保存完整环境数据"""
        try:
            os.makedirs("browser_environments", exist_ok=True)
            
            environment_data = {
                'environment_id': self.environment_id,
                'phone_number': self.phone_number,
                'timestamp': datetime.now().isoformat(),
                'fingerprint': fingerprint,
                'cookies': cookies,
                'login_success': True,
                'version': '2.0_fixed'
            }
            
            # 保存环境文件
            with open(self.environment_file, 'w', encoding='utf-8') as f:
                json.dump(environment_data, f, ensure_ascii=False, indent=2)
            
            # 单独保存cookies文件（兼容性）
            with open(self.cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 环境数据已保存: {self.environment_file}")
            print(f"✅ Cookies已保存: {self.cookie_file}")
            
        except Exception as e:
            print(f"❌ 保存环境数据失败: {e}")
    
    async def load_environment(self, environment_file: str) -> bool:
        """加载并还原环境"""
        try:
            if not os.path.exists(environment_file):
                print(f"❌ 环境文件不存在: {environment_file}")
                return False
            
            with open(environment_file, 'r', encoding='utf-8') as f:
                environment_data = json.load(f)
            
            fingerprint = environment_data.get('fingerprint', {})
            cookies = environment_data.get('cookies', [])
            
            print("🔄 还原浏览器环境...")
            
            # 还原cookies
            if cookies:
                await self.context.add_cookies(cookies)
                print(f"✅ 已还原 {len(cookies)} 个cookies")
            
            # 还原环境参数（通过脚本注入）
            restore_script = f"""
                // 还原用户代理
                Object.defineProperty(navigator, 'userAgent', {{
                    get: () => '{fingerprint.get("userAgent", "")}',
                }});
                
                // 还原语言设置
                Object.defineProperty(navigator, 'language', {{
                    get: () => '{fingerprint.get("language", "zh-CN")}',
                }});
                
                // 还原平台信息
                Object.defineProperty(navigator, 'platform', {{
                    get: () => '{fingerprint.get("platform", "Win32")}',
                }});
            """
            
            await self.page.add_init_script(restore_script)
            print("✅ 环境参数还原完成")
            
            return True
            
        except Exception as e:
            print(f"❌ 环境还原失败: {e}")
            return False
    
    async def validate_login_with_retry(self) -> bool:
        """带重试的登录验证"""
        max_retries = 3
        retry_delay = 5
        
        for attempt in range(max_retries):
            try:
                print(f"🔍 验证登录状态 (尝试 {attempt + 1}/{max_retries})...")
                
                # 访问用户信息API
                response = await self.page.evaluate("""
                    async () => {
                        try {
                            const res = await fetch('/api/sns/web/v2/user/me', {
                                credentials: 'include',
                                headers: {
                                    'Accept': 'application/json, text/plain, */*',
                                    'X-Requested-With': 'XMLHttpRequest'
                                }
                            });
                            const data = await res.json();
                            return { success: res.ok, data: data, status: res.status };
                        } catch (e) {
                            return { success: false, error: e.message };
                        }
                    }
                """)
                
                if response.get('success') and response.get('data', {}).get('success'):
                    user_data = response['data'].get('data', {})
                    if user_data.get('user_id'):
                        print(f"✅ 登录验证成功! 用户: {user_data.get('nickname', '未知')}")
                        return True
                
                print(f"⚠️ 第 {attempt + 1} 次验证失败: {response}")
                
                if attempt < max_retries - 1:
                    print(f"⏳ 等待 {retry_delay} 秒后重试...")
                    await asyncio.sleep(retry_delay)
                
            except Exception as e:
                print(f"❌ 验证过程出错: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
        
        print("❌ 所有验证尝试都失败了")
        return False
    
    async def close(self):
        """关闭浏览器"""
        try:
            if self.browser:
                await self.browser.close()
                print("🔒 浏览器已关闭")
        except Exception as e:
            print(f"⚠️ 关闭浏览器时出错: {e}")
