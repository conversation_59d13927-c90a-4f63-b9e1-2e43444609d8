#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版系统运行脚本
"""

import asyncio
import sys
import os
import subprocess
from datetime import datetime

class FixedSystemRunner:
    """修复版系统运行器"""
    
    def __init__(self):
        self.python_cmd = sys.executable
        
    def check_dependencies(self) -> bool:
        """检查依赖"""
        print("🔍 检查系统依赖...")
        
        try:
            # 检查Python版本
            python_version = sys.version_info
            if python_version.major < 3 or python_version.minor < 8:
                print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
                print("   需要Python 3.8或更高版本")
                return False
            
            print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
            
            # 检查必需的包
            required_packages = ['playwright', 'asyncio']
            missing_packages = []
            
            for package in required_packages:
                try:
                    __import__(package)
                    print(f"✅ {package} 已安装")
                except ImportError:
                    missing_packages.append(package)
                    print(f"❌ {package} 未安装")
            
            if missing_packages:
                print(f"\n📦 需要安装缺失的包:")
                for package in missing_packages:
                    print(f"   pip install {package}")
                return False
            
            # 检查playwright浏览器
            try:
                result = subprocess.run([self.python_cmd, '-m', 'playwright', 'install', '--help'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print("✅ Playwright 可用")
                else:
                    print("⚠️ Playwright 可能需要安装浏览器")
                    print("   运行: python -m playwright install chromium")
            except Exception as e:
                print(f"⚠️ Playwright 检查失败: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ 依赖检查失败: {e}")
            return False
    
    def setup_environment(self) -> bool:
        """设置环境"""
        print("🛠️ 设置运行环境...")
        
        try:
            # 创建必要目录
            directories = [
                "browser_environments",
                "screenshots", 
                "logs"
            ]
            
            for directory in directories:
                if not os.path.exists(directory):
                    os.makedirs(directory, exist_ok=True)
                    print(f"✅ 创建目录: {directory}")
                else:
                    print(f"✅ 目录已存在: {directory}")
            
            # 检查必要文件
            required_files = [
                "fixed_browser_environment.py",
                "fixed_login_service.py",
                "fixed_cookie_validator.py",
                "test_fixed_login.py"
            ]
            
            missing_files = []
            for file_name in required_files:
                if not os.path.exists(file_name):
                    missing_files.append(file_name)
                else:
                    print(f"✅ 文件存在: {file_name}")
            
            if missing_files:
                print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 环境设置失败: {e}")
            return False
    
    async def run_system_test(self, phone_number: str) -> bool:
        """运行系统测试"""
        print("🧪 运行系统测试...")
        
        try:
            # 导入测试模块
            from test_fixed_login import LoginSystemTester
            
            tester = LoginSystemTester()
            success = await tester.run_comprehensive_test(phone_number, include_login=False)
            
            return success
            
        except Exception as e:
            print(f"❌ 系统测试失败: {e}")
            return False
    
    async def run_login_service(self, phone_number: str) -> bool:
        """运行登录服务"""
        print("🚀 启动登录服务...")
        
        try:
            from fixed_login_service import FixedLoginService
            
            login_service = FixedLoginService(phone_number)
            success = await login_service.run_fixed_login()
            
            if success:
                print("✅ 登录服务执行成功")
            else:
                print("❌ 登录服务执行失败")
            
            await login_service.close()
            return success
            
        except Exception as e:
            print(f"❌ 登录服务运行失败: {e}")
            return False
    
    async def validate_existing_cookies(self) -> bool:
        """验证现有cookies"""
        print("🔍 验证现有cookies...")
        
        try:
            from fixed_cookie_validator import FixedCookieValidator
            
            validator = FixedCookieValidator()
            valid_env = await validator.find_valid_environment()
            
            if valid_env:
                is_valid, details = await validator.validate_environment_cookies(valid_env)
                
                if is_valid:
                    print(f"✅ 找到有效环境: {os.path.basename(valid_env)}")
                    print(f"   成功率: {details.get('success_rate', 0):.1%}")
                    return True
                else:
                    print(f"❌ 环境无效: {details.get('error', '未知错误')}")
                    return False
            else:
                print("❌ 未找到有效的环境文件")
                return False
                
        except Exception as e:
            print(f"❌ Cookie验证失败: {e}")
            return False
    
    def show_usage(self):
        """显示使用说明"""
        print("🎯 修复版小红书登录系统")
        print("=" * 40)
        print("使用方法:")
        print(f"  {sys.argv[0]} <命令> [参数]")
        print("")
        print("命令:")
        print("  check                    - 检查系统依赖")
        print("  setup                    - 设置运行环境")
        print("  test <手机号>            - 运行系统测试")
        print("  login <手机号>           - 执行登录")
        print("  validate                 - 验证现有cookies")
        print("  full <手机号>            - 完整流程（检查+设置+测试+登录）")
        print("")
        print("示例:")
        print(f"  {sys.argv[0]} full 13800138000")
        print(f"  {sys.argv[0]} validate")
        print(f"  {sys.argv[0]} login 13800138000")
    
    async def run_full_process(self, phone_number: str) -> bool:
        """运行完整流程"""
        print("🎯 开始完整修复流程")
        print("=" * 50)
        
        # 1. 检查依赖
        if not self.check_dependencies():
            print("❌ 依赖检查失败，请先安装必要依赖")
            return False
        
        # 2. 设置环境
        if not self.setup_environment():
            print("❌ 环境设置失败")
            return False
        
        # 3. 运行测试
        test_success = await self.run_system_test(phone_number)
        if not test_success:
            print("❌ 系统测试失败")
            return False
        
        # 4. 检查现有cookies
        has_valid_cookies = await self.validate_existing_cookies()
        
        if has_valid_cookies:
            print("✅ 发现有效的cookies，无需重新登录")
            return True
        
        # 5. 执行登录
        print("\n🔐 开始登录流程...")
        login_success = await self.run_login_service(phone_number)
        
        if login_success:
            print("\n🎉 完整流程执行成功！")
            print("📁 环境文件已保存到 browser_environments/ 目录")
            print("🚀 现在可以使用举报工具了")
            return True
        else:
            print("\n❌ 登录流程失败")
            return False

async def main():
    """主函数"""
    runner = FixedSystemRunner()
    
    if len(sys.argv) < 2:
        runner.show_usage()
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    try:
        if command == "check":
            success = runner.check_dependencies()
            
        elif command == "setup":
            success = runner.setup_environment()
            
        elif command == "test":
            if len(sys.argv) < 3:
                print("❌ 请提供手机号")
                sys.exit(1)
            phone_number = sys.argv[2]
            success = await runner.run_system_test(phone_number)
            
        elif command == "login":
            if len(sys.argv) < 3:
                print("❌ 请提供手机号")
                sys.exit(1)
            phone_number = sys.argv[2]
            success = await runner.run_login_service(phone_number)
            
        elif command == "validate":
            success = await runner.validate_existing_cookies()
            
        elif command == "full":
            if len(sys.argv) < 3:
                print("❌ 请提供手机号")
                sys.exit(1)
            phone_number = sys.argv[2]
            
            if not phone_number.isdigit() or len(phone_number) != 11:
                print("❌ 请输入有效的11位手机号")
                sys.exit(1)
            
            success = await runner.run_full_process(phone_number)
            
        else:
            print(f"❌ 未知命令: {command}")
            runner.show_usage()
            sys.exit(1)
        
        if success:
            print(f"\n✅ 命令 '{command}' 执行成功")
        else:
            print(f"\n❌ 命令 '{command}' 执行失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"\n💥 执行过程出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
