#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版Cookie验证器
解决cookie验证逻辑缺陷
"""

import json
import os
import asyncio
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from fixed_browser_environment import FixedBrowserEnvironment

class FixedCookieValidator:
    """修复版Cookie验证器"""
    
    def __init__(self):
        self.validation_cache = {}
        self.cache_timeout = 300  # 5分钟缓存
        
    def _get_cache_key(self, environment_id: str) -> str:
        """获取缓存键"""
        return f"validation_{environment_id}"
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.validation_cache:
            return False
        
        cache_time = self.validation_cache[cache_key].get('timestamp', 0)
        return time.time() - cache_time < self.cache_timeout
    
    async def validate_environment_cookies(self, environment_file: str) -> Tu<PERSON>[bool, Dict]:
        """验证环境中的cookies"""
        try:
            if not os.path.exists(environment_file):
                return False, {"error": "环境文件不存在"}
            
            with open(environment_file, 'r', encoding='utf-8') as f:
                environment_data = json.load(f)
            
            environment_id = environment_data.get('environment_id', '')
            cache_key = self._get_cache_key(environment_id)
            
            # 检查缓存
            if self._is_cache_valid(cache_key):
                cached_result = self.validation_cache[cache_key]
                print(f"✅ 使用缓存的验证结果: {cached_result['valid']}")
                return cached_result['valid'], cached_result['details']
            
            phone_number = environment_data.get('phone_number', '')
            cookies = environment_data.get('cookies', [])
            fingerprint = environment_data.get('fingerprint', {})
            
            if not cookies:
                return False, {"error": "环境中没有cookies"}
            
            print(f"🔍 验证环境 {environment_id} 的cookies...")
            
            # 创建临时环境进行验证
            temp_env = FixedBrowserEnvironment(phone_number)
            
            try:
                # 初始化浏览器
                if not await temp_env.init_browser():
                    return False, {"error": "浏览器初始化失败"}
                
                # 还原环境
                await temp_env.context.add_cookies(cookies)
                
                # 多重验证
                validation_results = await self._perform_multiple_validations(temp_env)
                
                # 计算总体有效性
                valid_count = sum(1 for result in validation_results.values() if result.get('valid', False))
                total_count = len(validation_results)
                
                is_valid = valid_count >= (total_count * 0.6)  # 60%通过率认为有效
                
                result_details = {
                    "environment_id": environment_id,
                    "phone_number": phone_number,
                    "cookie_count": len(cookies),
                    "validation_results": validation_results,
                    "valid_count": valid_count,
                    "total_count": total_count,
                    "success_rate": valid_count / total_count if total_count > 0 else 0,
                    "timestamp": datetime.now().isoformat()
                }
                
                # 更新缓存
                self.validation_cache[cache_key] = {
                    "valid": is_valid,
                    "details": result_details,
                    "timestamp": time.time()
                }
                
                return is_valid, result_details
                
            finally:
                await temp_env.close()
                
        except Exception as e:
            error_details = {"error": f"验证过程出错: {e}"}
            return False, error_details
    
    async def _perform_multiple_validations(self, environment: FixedBrowserEnvironment) -> Dict:
        """执行多重验证"""
        validations = {}
        
        # 验证1: 用户信息API
        validations['user_api'] = await self._validate_user_api(environment)
        
        # 验证2: 首页访问
        validations['homepage'] = await self._validate_homepage_access(environment)
        
        # 验证3: 笔记页面访问
        validations['note_access'] = await self._validate_note_access(environment)
        
        # 验证4: 搜索功能
        validations['search'] = await self._validate_search_function(environment)
        
        return validations
    
    async def _validate_user_api(self, environment: FixedBrowserEnvironment) -> Dict:
        """验证用户信息API"""
        try:
            print("🔍 验证用户信息API...")
            
            await environment.page.goto('https://www.xiaohongshu.com', timeout=30000)
            await asyncio.sleep(3)
            
            response = await environment.page.evaluate("""
                async () => {
                    try {
                        const res = await fetch('/api/sns/web/v2/user/me', {
                            credentials: 'include',
                            headers: {
                                'Accept': 'application/json, text/plain, */*',
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });
                        const data = await res.json();
                        return { 
                            success: res.ok, 
                            status: res.status,
                            data: data,
                            hasUserId: !!(data.data && data.data.user_id)
                        };
                    } catch (e) {
                        return { success: false, error: e.message };
                    }
                }
            """)
            
            is_valid = (response.get('success', False) and 
                       response.get('data', {}).get('success', False) and
                       response.get('hasUserId', False))
            
            return {
                "valid": is_valid,
                "response": response,
                "test_name": "用户信息API"
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": str(e),
                "test_name": "用户信息API"
            }
    
    async def _validate_homepage_access(self, environment: FixedBrowserEnvironment) -> Dict:
        """验证首页访问"""
        try:
            print("🔍 验证首页访问...")
            
            await environment.page.goto('https://www.xiaohongshu.com/explore', timeout=30000)
            await asyncio.sleep(3)
            
            # 检查是否有登录按钮
            has_login_button = await environment.page.evaluate("""
                () => {
                    const loginElements = document.querySelectorAll('button, a, div');
                    for (const el of loginElements) {
                        const text = (el.textContent || el.innerText || '').trim();
                        if (text.includes('登录') || text.includes('登陆')) {
                            return true;
                        }
                    }
                    return false;
                }
            """)
            
            # 检查是否有用户相关元素
            has_user_elements = await environment.page.evaluate("""
                () => {
                    const userElements = document.querySelectorAll('[class*="avatar"], [class*="user"], [class*="profile"], [class*="nickname"]');
                    return userElements.length > 0;
                }
            """)
            
            is_valid = not has_login_button and has_user_elements
            
            return {
                "valid": is_valid,
                "has_login_button": has_login_button,
                "has_user_elements": has_user_elements,
                "test_name": "首页访问"
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": str(e),
                "test_name": "首页访问"
            }
    
    async def _validate_note_access(self, environment: FixedBrowserEnvironment) -> Dict:
        """验证笔记页面访问"""
        try:
            print("🔍 验证笔记页面访问...")
            
            # 使用一个测试笔记URL
            test_url = "https://www.xiaohongshu.com/explore/688b459c0000000025024a94"
            
            await environment.page.goto(test_url, timeout=30000)
            await asyncio.sleep(3)
            
            # 检查页面内容
            page_content = await environment.page.evaluate("""
                () => {
                    const body = document.body;
                    const text = body.textContent || body.innerText || '';
                    return {
                        hasContent: text.length > 100,
                        hasLoginPrompt: text.includes('登录') || text.includes('登陆'),
                        hasNoteContent: text.includes('笔记') || text.includes('内容') || text.includes('作者'),
                        url: window.location.href
                    };
                }
            """)
            
            is_valid = (page_content.get('hasContent', False) and 
                       not page_content.get('hasLoginPrompt', True) and
                       page_content.get('hasNoteContent', False))
            
            return {
                "valid": is_valid,
                "page_content": page_content,
                "test_name": "笔记页面访问"
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": str(e),
                "test_name": "笔记页面访问"
            }
    
    async def _validate_search_function(self, environment: FixedBrowserEnvironment) -> Dict:
        """验证搜索功能"""
        try:
            print("🔍 验证搜索功能...")
            
            await environment.page.goto('https://www.xiaohongshu.com/search_result?keyword=测试', timeout=30000)
            await asyncio.sleep(3)
            
            # 检查搜索结果页面
            search_result = await environment.page.evaluate("""
                () => {
                    const body = document.body;
                    const text = body.textContent || body.innerText || '';
                    return {
                        hasResults: text.includes('搜索') || text.includes('结果'),
                        hasLoginPrompt: text.includes('登录') || text.includes('登陆'),
                        url: window.location.href
                    };
                }
            """)
            
            is_valid = (search_result.get('hasResults', False) and 
                       not search_result.get('hasLoginPrompt', True))
            
            return {
                "valid": is_valid,
                "search_result": search_result,
                "test_name": "搜索功能"
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": str(e),
                "test_name": "搜索功能"
            }
    
    async def find_valid_environment(self) -> Optional[str]:
        """查找有效的环境文件"""
        try:
            env_dir = "browser_environments"
            if not os.path.exists(env_dir):
                return None
            
            env_files = [f for f in os.listdir(env_dir) if f.startswith('fixed_') and f.endswith('.json')]
            
            if not env_files:
                return None
            
            # 按修改时间排序，最新的在前
            env_files.sort(key=lambda x: os.path.getmtime(os.path.join(env_dir, x)), reverse=True)
            
            for env_file in env_files:
                env_path = os.path.join(env_dir, env_file)
                is_valid, details = await self.validate_environment_cookies(env_path)
                
                if is_valid:
                    print(f"✅ 找到有效环境: {env_file}")
                    return env_path
                else:
                    print(f"❌ 环境无效: {env_file} - {details.get('error', '未知错误')}")
            
            return None
            
        except Exception as e:
            print(f"❌ 查找环境时出错: {e}")
            return None

async def main():
    """主函数 - 测试cookie验证器"""
    validator = FixedCookieValidator()
    
    print("🔍 查找并验证有效的环境...")
    valid_env = await validator.find_valid_environment()
    
    if valid_env:
        print(f"✅ 找到有效环境: {valid_env}")
        
        # 详细验证
        is_valid, details = await validator.validate_environment_cookies(valid_env)
        
        print(f"\n📊 验证结果:")
        print(f"   有效性: {'✅ 有效' if is_valid else '❌ 无效'}")
        print(f"   成功率: {details.get('success_rate', 0):.1%}")
        print(f"   通过测试: {details.get('valid_count', 0)}/{details.get('total_count', 0)}")
        
        for test_name, result in details.get('validation_results', {}).items():
            status = "✅" if result.get('valid', False) else "❌"
            print(f"   {status} {result.get('test_name', test_name)}")
    else:
        print("❌ 未找到有效的环境文件")

if __name__ == "__main__":
    asyncio.run(main())
