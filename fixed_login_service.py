#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版登录服务
解决短信登录循环和cookie失效问题
"""

import asyncio
import time
from datetime import datetime
from fixed_browser_environment import FixedBrowserEnvironment

class FixedLoginService:
    """修复版登录服务"""
    
    def __init__(self, phone_number: str):
        self.phone_number = phone_number
        self.environment = FixedBrowserEnvironment(phone_number)
        self.login_attempts = 0
        self.max_attempts = 3
        self.cooldown_time = 60  # 60秒冷却
        self.last_attempt_time = 0
        
    async def check_cooldown(self) -> bool:
        """检查是否在冷却期"""
        current_time = time.time()
        if current_time - self.last_attempt_time < self.cooldown_time:
            remaining = int(self.cooldown_time - (current_time - self.last_attempt_time))
            print(f"⏳ 登录冷却中，还需等待 {remaining} 秒")
            return False
        return True
    
    async def send_sms_code(self) -> bool:
        """发送短信验证码 - 修复版"""
        try:
            print(f"📱 发送验证码到: {self.phone_number}")

            # 访问小红书首页
            await self.environment.page.goto('https://www.xiaohongshu.com',
                                            wait_until='domcontentloaded',
                                            timeout=30000)
            await asyncio.sleep(3)

            # 尝试多种方式发送验证码
            success = False

            # 方式1: 尝试API调用
            print("🔄 尝试方式1: API调用...")
            success = await self._send_code_via_api()

            if not success:
                # 方式2: 尝试UI自动化
                print("🔄 尝试方式2: UI自动化...")
                success = await self._send_code_via_ui()

            if not success:
                # 方式3: 尝试手动引导
                print("🔄 尝试方式3: 手动引导...")
                success = await self._send_code_manual_guide()

            return success

        except Exception as e:
            print(f"❌ 发送验证码失败: {e}")
            return False

    async def _send_code_via_api(self) -> bool:
        """通过API发送验证码"""
        try:
            # 多个API端点尝试
            api_endpoints = [
                f"/api/sns/web/v2/login/send_code?phone={self.phone_number}&zone=86&type=login",
                f"/api/sns/web/v1/login/send_code?phone={self.phone_number}&zone=86",
                f"/api/sns/web/v2/login/sms?phone={self.phone_number}&zone=86"
            ]

            for i, endpoint in enumerate(api_endpoints):
                print(f"📡 尝试API端点 {i+1}: {endpoint}")

                result = await self.environment.page.evaluate(f"""
                    async () => {{
                        try {{
                            // 先获取页面的基本信息
                            const cookies = document.cookie;
                            const userAgent = navigator.userAgent;
                            const referer = window.location.href;

                            const response = await fetch('{endpoint}', {{
                                method: 'GET',
                                credentials: 'include',
                                headers: {{
                                    'Accept': 'application/json, text/plain, */*',
                                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                                    'Cache-Control': 'no-cache',
                                    'Pragma': 'no-cache',
                                    'Referer': referer,
                                    'User-Agent': userAgent,
                                    'X-Requested-With': 'XMLHttpRequest'
                                }}
                            }});

                            const text = await response.text();
                            let data;
                            try {{
                                data = JSON.parse(text);
                            }} catch (e) {{
                                data = {{ error: '响应格式错误', text: text }};
                            }}

                            return {{
                                status: response.status,
                                ok: response.ok,
                                data: data,
                                endpoint: '{endpoint}'
                            }};
                        }} catch (error) {{
                            return {{
                                success: false,
                                error: error.message,
                                endpoint: '{endpoint}'
                            }};
                        }}
                    }}
                """)

                print(f"📨 API端点 {i+1} 结果: {result}")

                # 检查是否成功
                if result.get('ok') and result.get('data'):
                    data = result['data']
                    if data.get('success') or (not data.get('error') and data.get('code') != -1):
                        print(f"✅ API端点 {i+1} 成功！")
                        return True

                await asyncio.sleep(1)

            print("❌ 所有API端点都失败了")
            return False

        except Exception as e:
            print(f"❌ API调用异常: {e}")
            return False

    async def _send_code_via_ui(self) -> bool:
        """通过UI自动化发送验证码"""
        try:
            # 点击登录按钮
            login_clicked = await self.environment.page.evaluate("""
                () => {
                    const loginBtns = document.querySelectorAll('button, a, div, span');
                    for (const btn of loginBtns) {
                        const text = (btn.textContent || btn.innerText || '').trim();
                        if (text.includes('登录') || text.includes('登陆')) {
                            btn.click();
                            return { success: true, text: text };
                        }
                    }
                    return { success: false };
                }
            """)

            if not login_clicked.get('success'):
                print("❌ 未找到登录按钮")
                return False

            print(f"✅ 点击登录按钮: {login_clicked.get('text', '')}")
            await asyncio.sleep(3)

            # 切换到手机号登录
            phone_tab_clicked = await self.environment.page.evaluate("""
                () => {
                    const tabs = document.querySelectorAll('div, span, button, a');
                    for (const tab of tabs) {
                        const text = (tab.textContent || tab.innerText || '').trim();
                        if (text.includes('手机号') || text.includes('验证码') || text.includes('短信')) {
                            tab.click();
                            return { success: true, text: text };
                        }
                    }
                    return { success: false };
                }
            """)

            if phone_tab_clicked.get('success'):
                print(f"✅ 切换到手机号登录: {phone_tab_clicked.get('text', '')}")

            await asyncio.sleep(2)

            # 输入手机号
            phone_input_result = await self.environment.page.evaluate(f"""
                (phone) => {{
                    const inputs = document.querySelectorAll('input[type="text"], input[type="tel"], input[placeholder*="手机"], input[placeholder*="号码"], input[name*="phone"]');
                    for (const input of inputs) {{
                        if (input.offsetParent !== null && !input.disabled) {{
                            input.focus();
                            input.value = '';
                            input.value = phone;
                            input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            input.dispatchEvent(new Event('blur', {{ bubbles: true }}));
                            return {{ success: true, placeholder: input.placeholder }};
                        }}
                    }}
                    return {{ success: false }};
                }}
            """, self.phone_number)

            if not phone_input_result.get('success'):
                print("❌ 未找到手机号输入框")
                return False

            print(f"✅ 手机号已输入: {self.phone_number}")
            await asyncio.sleep(2)

            # 点击发送验证码按钮
            sms_result = await self.environment.page.evaluate("""
                () => {
                    const buttons = document.querySelectorAll('button, div, span, a');
                    const results = [];

                    for (const btn of buttons) {
                        const text = (btn.textContent || btn.innerText || '').trim();
                        if (text.includes('获取验证码') || text.includes('发送验证码') ||
                            text.includes('验证码') || text.includes('发送') || text.includes('获取')) {
                            if (!btn.disabled && btn.offsetParent !== null) {
                                results.push({ element: btn, text: text });
                            }
                        }
                    }

                    if (results.length > 0) {
                        // 选择最合适的按钮
                        const bestBtn = results.find(r => r.text.includes('获取验证码')) ||
                                       results.find(r => r.text.includes('发送验证码')) ||
                                       results[0];

                        bestBtn.element.click();
                        return { success: true, text: bestBtn.text, count: results.length };
                    }

                    return { success: false, count: 0 };
                }
            """)

            if sms_result.get('success'):
                print(f"✅ 点击发送验证码按钮: {sms_result.get('text', '')}")
                await asyncio.sleep(3)

                # 检查是否出现验证码输入框
                code_input_appeared = await self.environment.page.evaluate("""
                    () => {
                        const inputs = document.querySelectorAll('input[type="text"], input[type="number"], input[placeholder*="验证码"], input[maxlength="6"]');
                        return inputs.length > 0;
                    }
                """)

                if code_input_appeared:
                    print("✅ 验证码输入框已出现，发送成功")
                    return True
                else:
                    print("⚠️ 未检测到验证码输入框，但按钮已点击")
                    return True
            else:
                print(f"❌ 未找到发送验证码按钮 (检查了 {sms_result.get('count', 0)} 个元素)")
                return False

        except Exception as e:
            print(f"❌ UI自动化失败: {e}")
            return False

    async def _send_code_manual_guide(self) -> bool:
        """手动引导发送验证码"""
        try:
            print("🔄 启动手动引导模式...")
            print("=" * 50)
            print("📋 请按照以下步骤手动操作:")
            print(f"1. 在浏览器中找到登录按钮并点击")
            print(f"2. 选择手机号登录方式")
            print(f"3. 输入手机号: {self.phone_number}")
            print(f"4. 点击'获取验证码'或'发送验证码'按钮")
            print("5. 完成后按回车键继续...")
            print("=" * 50)

            # 等待用户操作
            input("⏳ 请完成上述操作后按回车键继续...")

            # 检查是否出现验证码输入框
            await asyncio.sleep(2)

            code_input_check = await self.environment.page.evaluate("""
                () => {
                    const inputs = document.querySelectorAll('input[type="text"], input[type="number"], input[placeholder*="验证码"], input[maxlength="6"]');
                    const codeInputs = [];

                    for (const input of inputs) {
                        if (input.offsetParent !== null) {
                            const placeholder = input.placeholder || '';
                            const name = input.name || '';
                            const id = input.id || '';

                            if (placeholder.includes('验证码') || name.includes('code') || id.includes('code') ||
                                input.maxLength === 6 || input.maxLength === 4) {
                                codeInputs.push({
                                    placeholder: placeholder,
                                    name: name,
                                    id: id,
                                    maxLength: input.maxLength
                                });
                            }
                        }
                    }

                    return {
                        hasCodeInput: codeInputs.length > 0,
                        codeInputs: codeInputs,
                        totalInputs: inputs.length
                    };
                }
            """)

            if code_input_check.get('hasCodeInput'):
                print("✅ 检测到验证码输入框，手动操作成功！")
                print(f"📝 验证码输入框信息: {code_input_check.get('codeInputs', [])}")
                return True
            else:
                print("⚠️ 未检测到验证码输入框")
                print(f"📊 页面输入框总数: {code_input_check.get('totalInputs', 0)}")

                # 询问用户是否已发送成功
                user_confirm = input("❓ 验证码是否已发送成功？(y/n): ").strip().lower()
                return user_confirm == 'y'

        except Exception as e:
            print(f"❌ 手动引导失败: {e}")
            return False
    
    async def complete_login(self, sms_code: str) -> bool:
        """完成登录 - 修复版"""
        try:
            print(f"🔐 输入验证码: {sms_code}")

            # 尝试多种方式输入验证码
            success = False

            # 方式1: 自动输入验证码
            success = await self._input_code_auto(sms_code)

            if not success:
                # 方式2: 手动引导输入
                success = await self._input_code_manual(sms_code)

            if not success:
                print("❌ 验证码输入失败")
                return False

            # 尝试提交登录
            login_success = await self._submit_login()

            if login_success:
                # 验证登录状态
                is_logged_in = await self.environment.validate_login_with_retry()

                if is_logged_in:
                    print("🎉 登录成功！")

                    # 收集环境指纹
                    fingerprint = await self.environment.collect_environment_fingerprint()

                    # 获取cookies
                    cookies = await self.environment.context.cookies()

                    # 保存完整环境
                    await self.environment.save_environment(cookies, fingerprint)

                    return True
                else:
                    print("❌ 登录验证失败")
                    return False
            else:
                print("❌ 登录提交失败")
                return False

        except Exception as e:
            print(f"❌ 登录过程出错: {e}")
            return False

    async def _input_code_auto(self, sms_code: str) -> bool:
        """自动输入验证码"""
        try:
            # 查找验证码输入框
            code_input_result = await self.environment.page.evaluate(f"""
                (code) => {{
                    const inputs = document.querySelectorAll('input[type="text"], input[type="number"], input[placeholder*="验证码"], input[maxlength="6"], input[maxlength="4"]');
                    const results = [];

                    for (const input of inputs) {{
                        if (input.offsetParent !== null && !input.disabled) {{
                            const placeholder = input.placeholder || '';
                            const name = input.name || '';
                            const id = input.id || '';

                            if (placeholder.includes('验证码') || name.includes('code') || id.includes('code') ||
                                input.maxLength === 6 || input.maxLength === 4) {{
                                results.push({{
                                    element: input,
                                    placeholder: placeholder,
                                    name: name,
                                    id: id,
                                    maxLength: input.maxLength
                                }});
                            }}
                        }}
                    }}

                    if (results.length > 0) {{
                        // 选择最合适的输入框
                        const bestInput = results.find(r => r.placeholder.includes('验证码')) ||
                                         results.find(r => r.maxLength === 6) ||
                                         results[0];

                        const input = bestInput.element;
                        input.focus();
                        input.value = '';
                        input.value = code;
                        input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        input.dispatchEvent(new Event('blur', {{ bubbles: true }}));

                        return {{ success: true, info: bestInput, count: results.length }};
                    }}

                    return {{ success: false, count: results.length }};
                }}
            """, sms_code)

            if code_input_result.get('success'):
                print(f"✅ 验证码已自动输入 (找到 {code_input_result.get('count', 0)} 个候选输入框)")
                await asyncio.sleep(2)
                return True
            else:
                print(f"❌ 未找到合适的验证码输入框 (检查了 {code_input_result.get('count', 0)} 个输入框)")
                return False

        except Exception as e:
            print(f"❌ 自动输入验证码失败: {e}")
            return False

    async def _input_code_manual(self, sms_code: str) -> bool:
        """手动引导输入验证码"""
        try:
            print("🔄 启动手动输入模式...")
            print("=" * 40)
            print(f"📋 请手动输入验证码: {sms_code}")
            print("1. 在浏览器中找到验证码输入框")
            print(f"2. 输入验证码: {sms_code}")
            print("3. 完成后按回车键继续...")
            print("=" * 40)

            input("⏳ 请完成验证码输入后按回车键继续...")

            # 检查验证码是否已输入
            code_check = await self.environment.page.evaluate(f"""
                (expectedCode) => {{
                    const inputs = document.querySelectorAll('input[type="text"], input[type="number"], input[placeholder*="验证码"], input[maxlength="6"], input[maxlength="4"]');

                    for (const input of inputs) {{
                        if (input.offsetParent !== null && input.value) {{
                            if (input.value === expectedCode || input.value.length >= 4) {{
                                return {{ success: true, value: input.value, expected: expectedCode }};
                            }}
                        }}
                    }}

                    return {{ success: false, expected: expectedCode }};
                }}
            """, sms_code)

            if code_check.get('success'):
                print(f"✅ 验证码已输入: {code_check.get('value', '')}")
                return True
            else:
                user_confirm = input("❓ 验证码是否已正确输入？(y/n): ").strip().lower()
                return user_confirm == 'y'

        except Exception as e:
            print(f"❌ 手动输入验证码失败: {e}")
            return False

    async def _submit_login(self) -> bool:
        """提交登录"""
        try:
            # 先尝试勾选用户协议
            agreement_result = await self.environment.page.evaluate("""
                () => {
                    const checkboxes = document.querySelectorAll('input[type="checkbox"], .checkbox, [role="checkbox"], [class*="checkbox"]');
                    let checkedCount = 0;

                    for (const checkbox of checkboxes) {
                        if (checkbox.offsetParent !== null) {
                            if (checkbox.type === 'checkbox' && !checkbox.checked) {
                                checkbox.checked = true;
                                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                                checkedCount++;
                            } else if (checkbox.getAttribute('role') === 'checkbox') {
                                checkbox.click();
                                checkedCount++;
                            }
                        }
                    }

                    return { checkedCount: checkedCount, totalFound: checkboxes.length };
                }
            """)

            if agreement_result.get('checkedCount', 0) > 0:
                print(f"✅ 已勾选 {agreement_result['checkedCount']} 个用户协议")

            await asyncio.sleep(1)

            # 查找并点击登录按钮
            login_result = await self.environment.page.evaluate("""
                () => {
                    const buttons = document.querySelectorAll('button, div[role="button"], a[role="button"], input[type="submit"]');
                    const loginButtons = [];

                    for (const btn of buttons) {
                        const text = (btn.textContent || btn.innerText || btn.value || '').trim();
                        if ((text.includes('登录') || text.includes('登陆') || text.includes('确认') || text.includes('提交')) &&
                            !btn.disabled && btn.offsetParent !== null) {
                            loginButtons.push({ element: btn, text: text });
                        }
                    }

                    if (loginButtons.length > 0) {
                        // 选择最合适的登录按钮
                        const bestBtn = loginButtons.find(b => b.text.includes('登录')) ||
                                       loginButtons.find(b => b.text.includes('登陆')) ||
                                       loginButtons[0];

                        bestBtn.element.click();
                        return { success: true, text: bestBtn.text, count: loginButtons.length };
                    }

                    return { success: false, count: loginButtons.length };
                }
            """)

            if login_result.get('success'):
                print(f"✅ 点击登录按钮: {login_result.get('text', '')}")
                print("⏳ 等待登录完成...")
                await asyncio.sleep(5)
                return True
            else:
                print(f"❌ 未找到登录按钮 (检查了 {login_result.get('count', 0)} 个按钮)")

                # 手动引导点击登录
                print("🔄 请手动点击登录按钮...")
                input("⏳ 点击登录按钮后按回车键继续...")
                await asyncio.sleep(3)
                return True

        except Exception as e:
            print(f"❌ 提交登录失败: {e}")
            return False
    
    async def run_fixed_login(self) -> bool:
        """执行修复版登录流程"""
        try:
            # 检查冷却时间
            if not await self.check_cooldown():
                return False
            
            # 检查尝试次数
            if self.login_attempts >= self.max_attempts:
                print(f"❌ 已达到最大登录尝试次数 ({self.max_attempts})")
                return False
            
            self.login_attempts += 1
            self.last_attempt_time = time.time()
            
            print(f"\n🎯 修复版小红书登录服务 (尝试 {self.login_attempts}/{self.max_attempts})")
            print("=" * 50)
            print(f"📱 手机号: {self.phone_number}")
            print(f"🆔 环境ID: {self.environment.environment_id}")
            print("")
            
            # 初始化浏览器环境
            if not await self.environment.init_browser():
                return False
            
            # 发送验证码
            if not await self.send_sms_code():
                print("❌ 验证码发送失败")
                return False
            
            # 获取用户输入的验证码
            sms_code = input("📱 请输入收到的验证码: ").strip()
            
            if not sms_code or len(sms_code) != 6:
                print("❌ 验证码格式不正确")
                return False
            
            # 完成登录
            login_success = await self.complete_login(sms_code)
            
            if login_success:
                print("\n🎉 登录完全成功！")
                print(f"📁 环境文件: {self.environment.environment_file}")
                print(f"🍪 Cookies文件: {self.environment.cookie_file}")
                print("🚀 现在可以使用举报工具了")
                
                # 重置尝试计数
                self.login_attempts = 0
                return True
            else:
                print(f"\n❌ 登录失败 (尝试 {self.login_attempts}/{self.max_attempts})")
                if self.login_attempts < self.max_attempts:
                    print(f"⏳ 将在 {self.cooldown_time} 秒后允许重试")
                return False
                
        except Exception as e:
            print(f"\n💥 登录流程异常: {e}")
            return False
        finally:
            # 不立即关闭浏览器，保持环境
            pass
    
    async def close(self):
        """关闭服务"""
        await self.environment.close()

async def main():
    """主函数"""
    import sys
    
    if len(sys.argv) != 2:
        print("使用方法: python fixed_login_service.py <手机号>")
        sys.exit(1)
    
    phone_number = sys.argv[1]
    
    if not phone_number.isdigit() or len(phone_number) != 11:
        print("❌ 请输入有效的11位手机号")
        sys.exit(1)
    
    login_service = FixedLoginService(phone_number)
    
    try:
        success = await login_service.run_fixed_login()
        if success:
            print("\n✅ 登录服务执行成功")
        else:
            print("\n❌ 登录服务执行失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    finally:
        await login_service.close()

if __name__ == "__main__":
    asyncio.run(main())
