#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版登录服务
解决短信登录循环和cookie失效问题
"""

import asyncio
import time
from datetime import datetime
from fixed_browser_environment import FixedBrowserEnvironment

class FixedLoginService:
    """修复版登录服务"""
    
    def __init__(self, phone_number: str):
        self.phone_number = phone_number
        self.environment = FixedBrowserEnvironment(phone_number)
        self.login_attempts = 0
        self.max_attempts = 3
        self.cooldown_time = 60  # 60秒冷却
        self.last_attempt_time = 0
        
    async def check_cooldown(self) -> bool:
        """检查是否在冷却期"""
        current_time = time.time()
        if current_time - self.last_attempt_time < self.cooldown_time:
            remaining = int(self.cooldown_time - (current_time - self.last_attempt_time))
            print(f"⏳ 登录冷却中，还需等待 {remaining} 秒")
            return False
        return True
    
    async def send_sms_code(self) -> bool:
        """发送短信验证码"""
        try:
            print(f"📱 发送验证码到: {self.phone_number}")
            
            # 访问小红书首页
            await self.environment.page.goto('https://www.xiaohongshu.com', 
                                            wait_until='domcontentloaded', 
                                            timeout=30000)
            await asyncio.sleep(3)
            
            # 点击登录按钮
            login_clicked = await self.environment.page.evaluate("""
                () => {
                    const loginBtns = document.querySelectorAll('button, a, div');
                    for (const btn of loginBtns) {
                        const text = (btn.textContent || btn.innerText || '').trim();
                        if (text.includes('登录') || text.includes('登陆')) {
                            btn.click();
                            return true;
                        }
                    }
                    return false;
                }
            """)
            
            if not login_clicked:
                print("❌ 未找到登录按钮")
                return False
            
            await asyncio.sleep(3)
            
            # 切换到手机号登录
            phone_tab_clicked = await self.environment.page.evaluate("""
                () => {
                    const tabs = document.querySelectorAll('div, span, button');
                    for (const tab of tabs) {
                        const text = (tab.textContent || tab.innerText || '').trim();
                        if (text.includes('手机号') || text.includes('验证码')) {
                            tab.click();
                            return true;
                        }
                    }
                    return false;
                }
            """)
            
            await asyncio.sleep(2)
            
            # 输入手机号
            phone_input_result = await self.environment.page.evaluate(f"""
                (phone) => {{
                    const inputs = document.querySelectorAll('input[type="text"], input[type="tel"], input[placeholder*="手机"], input[placeholder*="号码"]');
                    for (const input of inputs) {{
                        if (input.offsetParent !== null) {{
                            input.focus();
                            input.value = '';
                            input.value = phone;
                            input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            return {{ success: true, selector: input.tagName + (input.className ? '.' + input.className.split(' ')[0] : '') }};
                        }}
                    }}
                    return {{ success: false }};
                }}
            """, self.phone_number)
            
            if not phone_input_result.get('success'):
                print("❌ 未找到手机号输入框")
                return False
            
            print(f"✅ 手机号已输入: {self.phone_number}")
            await asyncio.sleep(2)
            
            # 点击发送验证码
            sms_clicked = await self.environment.page.evaluate("""
                () => {
                    const buttons = document.querySelectorAll('button, div, span');
                    for (const btn of buttons) {
                        const text = (btn.textContent || btn.innerText || '').trim();
                        if (text.includes('获取验证码') || text.includes('发送验证码') || text.includes('验证码')) {
                            if (!btn.disabled && btn.offsetParent !== null) {
                                btn.click();
                                return true;
                            }
                        }
                    }
                    return false;
                }
            """)
            
            if sms_clicked:
                print("✅ 验证码发送请求已提交")
                await asyncio.sleep(3)
                return True
            else:
                print("❌ 未找到发送验证码按钮")
                return False
                
        except Exception as e:
            print(f"❌ 发送验证码失败: {e}")
            return False
    
    async def complete_login(self, sms_code: str) -> bool:
        """完成登录"""
        try:
            print(f"🔐 输入验证码: {sms_code}")
            
            # 输入验证码
            code_input_result = await self.environment.page.evaluate(f"""
                (code) => {{
                    const inputs = document.querySelectorAll('input[type="text"], input[type="number"], input[placeholder*="验证码"], input[maxlength="6"]');
                    for (const input of inputs) {{
                        if (input.offsetParent !== null && !input.value) {{
                            input.focus();
                            input.value = '';
                            input.value = code;
                            input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            return {{ success: true }};
                        }}
                    }}
                    return {{ success: false }};
                }}
            """, sms_code)
            
            if not code_input_result.get('success'):
                print("❌ 未找到验证码输入框")
                return False
            
            await asyncio.sleep(2)
            
            # 勾选用户协议
            agreement_checked = await self.environment.page.evaluate("""
                () => {
                    const checkboxes = document.querySelectorAll('input[type="checkbox"], .checkbox, [role="checkbox"]');
                    for (const checkbox of checkboxes) {
                        if (checkbox.offsetParent !== null) {
                            if (checkbox.type === 'checkbox') {
                                checkbox.checked = true;
                                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                            } else {
                                checkbox.click();
                            }
                            return true;
                        }
                    }
                    return false;
                }
            """)
            
            if agreement_checked:
                print("✅ 用户协议已勾选")
            
            await asyncio.sleep(1)
            
            # 点击登录按钮
            login_clicked = await self.environment.page.evaluate("""
                () => {
                    const buttons = document.querySelectorAll('button, div[role="button"]');
                    for (const btn of buttons) {
                        const text = (btn.textContent || btn.innerText || '').trim();
                        if ((text.includes('登录') || text.includes('登陆')) && !btn.disabled && btn.offsetParent !== null) {
                            btn.click();
                            return true;
                        }
                    }
                    return false;
                }
            """)
            
            if not login_clicked:
                print("❌ 未找到登录按钮")
                return False
            
            print("✅ 登录请求已提交")
            
            # 等待登录完成
            print("⏳ 等待登录完成...")
            await asyncio.sleep(5)
            
            # 验证登录状态
            is_logged_in = await self.environment.validate_login_with_retry()
            
            if is_logged_in:
                print("🎉 登录成功！")
                
                # 收集环境指纹
                fingerprint = await self.environment.collect_environment_fingerprint()
                
                # 获取cookies
                cookies = await self.environment.context.cookies()
                
                # 保存完整环境
                await self.environment.save_environment(cookies, fingerprint)
                
                return True
            else:
                print("❌ 登录验证失败")
                return False
                
        except Exception as e:
            print(f"❌ 登录过程出错: {e}")
            return False
    
    async def run_fixed_login(self) -> bool:
        """执行修复版登录流程"""
        try:
            # 检查冷却时间
            if not await self.check_cooldown():
                return False
            
            # 检查尝试次数
            if self.login_attempts >= self.max_attempts:
                print(f"❌ 已达到最大登录尝试次数 ({self.max_attempts})")
                return False
            
            self.login_attempts += 1
            self.last_attempt_time = time.time()
            
            print(f"\n🎯 修复版小红书登录服务 (尝试 {self.login_attempts}/{self.max_attempts})")
            print("=" * 50)
            print(f"📱 手机号: {self.phone_number}")
            print(f"🆔 环境ID: {self.environment.environment_id}")
            print("")
            
            # 初始化浏览器环境
            if not await self.environment.init_browser():
                return False
            
            # 发送验证码
            if not await self.send_sms_code():
                print("❌ 验证码发送失败")
                return False
            
            # 获取用户输入的验证码
            sms_code = input("📱 请输入收到的验证码: ").strip()
            
            if not sms_code or len(sms_code) != 6:
                print("❌ 验证码格式不正确")
                return False
            
            # 完成登录
            login_success = await self.complete_login(sms_code)
            
            if login_success:
                print("\n🎉 登录完全成功！")
                print(f"📁 环境文件: {self.environment.environment_file}")
                print(f"🍪 Cookies文件: {self.environment.cookie_file}")
                print("🚀 现在可以使用举报工具了")
                
                # 重置尝试计数
                self.login_attempts = 0
                return True
            else:
                print(f"\n❌ 登录失败 (尝试 {self.login_attempts}/{self.max_attempts})")
                if self.login_attempts < self.max_attempts:
                    print(f"⏳ 将在 {self.cooldown_time} 秒后允许重试")
                return False
                
        except Exception as e:
            print(f"\n💥 登录流程异常: {e}")
            return False
        finally:
            # 不立即关闭浏览器，保持环境
            pass
    
    async def close(self):
        """关闭服务"""
        await self.environment.close()

async def main():
    """主函数"""
    import sys
    
    if len(sys.argv) != 2:
        print("使用方法: python fixed_login_service.py <手机号>")
        sys.exit(1)
    
    phone_number = sys.argv[1]
    
    if not phone_number.isdigit() or len(phone_number) != 11:
        print("❌ 请输入有效的11位手机号")
        sys.exit(1)
    
    login_service = FixedLoginService(phone_number)
    
    try:
        success = await login_service.run_fixed_login()
        if success:
            print("\n✅ 登录服务执行成功")
        else:
            print("\n❌ 登录服务执行失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    finally:
        await login_service.close()

if __name__ == "__main__":
    asyncio.run(main())
